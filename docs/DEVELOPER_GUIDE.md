# Developer Guide: Generic Structured Extractions with paperscraper

This guide shows how to build any LLM-powered structured extraction using the generic interfaces in this repo.

## Key concepts

- BaseLanguageModel (paperscraper/inference.py): batch generation and provider abstraction (OpenAI, Ollama, or custom)
- BasePromptBuilder (paperscraper/prompting.py): build per-item payloads with:
  - messages: chat messages for the model
  - response_format: optional Pydantic model for structured output parsing
  - result_key: where to store the structured object in the result dict

## Minimal steps

1) Define your schema (Pydantic)

```
from pydantic import BaseModel
from typing import List

class ProductSpec(BaseModel):
    name: str
    price: float
    features: List[str]
```

2) Create a PromptBuilder

```
from paperscraper.prompting import BasePromptBuilder
from .my_schema import ProductSpec

class ProductPrompt(BasePromptBuilder):
    def build(self, item):
        text = item["text"]
        return {
            "messages": [
                {"role": "user", "content": f"Extract ProductSpec as JSON from this text and adhere strictly to the schema.\n{text}"}
            ],
            "response_format": ProductSpec,  # critical for structured output
            "result_key": "product",
        }
```

3) Run a batch

```
from paperscraper.inference import get_language_model
from .my_prompt import ProductPrompt

items = [{"text": "Widget X costs $19.99 and includes wifi, gps."}]
inputs = [ProductPrompt().build(x) for x in items]

lm = get_language_model(provider="openai")  # or "ollama"
results, workers = lm.generate(inputs)
print(results[0]["product"])  # -> dict compatible with ProductSpec
```

## Error handling and expectations

- If response_format is omitted, providers will still call parse() with None. Prefer always supplying a Pydantic model when you want structured output.
- If build returns no messages, generation will fail; ensure prompts always include messages.
- Results include token counts and your result_key object or None with an error string if an exception occurred.

## Custom providers

- Implement BaseLanguageModel and register:

```
from paperscraper import BaseLanguageModel, register_language_model

class MyLM(BaseLanguageModel):
    def generate(self, inputs, *, max_workers=None):
        ...

register_language_model("myprovider", lambda **kw: MyLM(**kw))
```

Use with provider="myprovider".

## arXiv affiliations task (example)

- Lives under paperscraper/tasks/arxiv_affiliations/
- Provides schema and ArxivAffiliationPromptBuilder used by the CLI when running the affiliations step.

## Where to put your code

- Examples: see paperscraper/examples/generic_extraction/
- For real tasks, create your own package or folder, import BasePromptBuilder and inference utilities, and run your extraction in your scripts.
