# Schema-Based Metrics System

The Schema-Based Metrics System provides comprehensive analysis of structured data against Pydantic schemas. It automatically analyzes each field based on its data type and provides detailed statistics including handling of None values, data validation, and nested object analysis.

## Features

### Supported Data Types

- **Numeric Fields (int, float)**: mean, median, min, max, standard deviation, percentiles
- **Boolean Fields**: true/false counts and percentages
- **String Fields**: length statistics, empty string detection
- **List Fields**: length statistics, item analysis for typed lists
- **Dictionary Fields**: key count analysis, unique key tracking
- **Optional Fields**: proper None value handling and tracking
- **Nested Objects**: recursive schema analysis for Pydantic models

### Key Capabilities

- **Automatic Type Detection**: Analyzes fields based on Pydantic schema annotations
- **None Value Handling**: Tracks None vs non-None values for all fields
- **Data Validation**: Identifies invalid values that don't match expected types
- **Nested Analysis**: Recursively analyzes nested Pydantic models
- **Comprehensive Statistics**: Provides detailed metrics appropriate for each data type
- **Edge Case Handling**: Gracefully handles empty datasets, all-None values, and invalid data

## Usage

### Basic Usage

```python
from pydantic import BaseModel
from typing import List, Optional
from extraction.metrics import SchemaMetricsCollector

# Define your schema
class Product(BaseModel):
    name: str
    price: float
    in_stock: bool
    features: List[str]
    description: Optional[str] = None

# Create metrics collector
collector = SchemaMetricsCollector(Product, "product_analysis")

# Your data (list of dictionaries)
products = [
    {
        "name": "Laptop",
        "price": 999.99,
        "in_stock": True,
        "features": ["16GB RAM", "512GB SSD"],
        "description": "High-performance laptop"
    },
    {
        "name": "Mouse",
        "price": 29.99,
        "in_stock": False,
        "features": ["Wireless", "Ergonomic"],
        "description": None
    }
]

# Collect metrics
collector.start_timing()
metrics = collector.collect_metrics(products)
collector.end_timing()

# Display results
print(collector.format_summary(metrics))
```

### Using the Factory Function

```python
from extraction.metrics import create_metrics_collector

collector = create_metrics_collector(
    collector_type="schema",
    name="my_analysis",
    schema_class=Product
)

metrics = collector.collect_metrics(data)
```

### Nested Objects

```python
class Customer(BaseModel):
    name: str
    age: int
    active: bool

class Order(BaseModel):
    order_id: str
    customer: Customer  # Nested object
    products: List[Product]  # List of nested objects
    total: float

collector = SchemaMetricsCollector(Order, "order_analysis")
# Automatically analyzes nested Customer and Product schemas
```

## Output Format

### Numeric Fields
```json
{
  "type": "float",
  "total_count": 100,
  "non_none_count": 95,
  "none_count": 5,
  "mean": 29.99,
  "median": 24.99,
  "min": 5.99,
  "max": 199.99,
  "std_dev": 15.2,
  "range": 194.0,
  "percentile_25": 19.99,
  "percentile_75": 39.99,
  "valid_numeric_count": 95,
  "invalid_values": 0
}
```

### Boolean Fields
```json
{
  "type": "bool",
  "total_count": 100,
  "non_none_count": 98,
  "none_count": 2,
  "true_count": 75,
  "false_count": 23,
  "true_percentage": 76.53,
  "false_percentage": 23.47,
  "valid_boolean_count": 98,
  "invalid_values": 0
}
```

### String Fields
```json
{
  "type": "str",
  "total_count": 100,
  "non_none_count": 90,
  "none_count": 10,
  "avg_length": 12.5,
  "min_length": 3,
  "max_length": 45,
  "median_length": 11.0,
  "empty_string_count": 2,
  "valid_string_count": 90,
  "invalid_values": 0,
  "total_characters": 1125
}
```

### List Fields
```json
{
  "type": "list[str]",
  "total_count": 100,
  "non_none_count": 90,
  "none_count": 10,
  "avg_length": 3.2,
  "min_length": 0,
  "max_length": 8,
  "median_length": 3.0,
  "empty_list_count": 5,
  "valid_list_count": 90,
  "invalid_values": 0,
  "total_items": 288,
  "item_metrics": {
    "type": "str",
    "avg_length": 8.5,
    "min_length": 2,
    "max_length": 25
  }
}
```

### Nested Objects
```json
{
  "type": "Customer",
  "total_count": 100,
  "non_none_count": 95,
  "none_count": 5,
  "nested_schema": "Customer",
  "valid_object_count": 95,
  "invalid_values": 0,
  "nested_metrics": {
    "name": { /* Customer name field metrics */ },
    "age": { /* Customer age field metrics */ },
    "active": { /* Customer active field metrics */ }
  }
}
```

## Advanced Features

### Data Validation Tracking

The system automatically tracks invalid values that don't match the expected schema types:

```python
# Data with invalid types
invalid_data = [
    {
        "name": "Product",
        "price": "not_a_number",  # Invalid: should be float
        "in_stock": "maybe",      # Invalid: should be bool
        "features": "not_a_list"  # Invalid: should be list
    }
]

metrics = collector.collect_metrics(invalid_data)

# Check for validation issues
for field_name, field_metrics in metrics["field_metrics"].items():
    if field_metrics.get("invalid_values", 0) > 0:
        print(f"{field_name}: {field_metrics['invalid_values']} invalid values")
```

### Optional Field Analysis

Optional fields (using `Optional[T]` or `T | None`) are automatically detected and analyzed:

```python
class Schema(BaseModel):
    required_field: str
    optional_field: Optional[str] = None

# The system will show:
# - is_optional: true/false for each field
# - Separate tracking of None vs non-None values
# - Analysis only performed on non-None values
```

### Performance Considerations

- **Memory Efficient**: Processes data in a single pass
- **Type-Aware**: Uses appropriate algorithms for each data type
- **Scalable**: Handles large datasets efficiently
- **Error Resilient**: Continues analysis even with invalid data

## Integration with Existing Metrics

The SchemaMetricsCollector integrates seamlessly with the existing metrics system:

```python
# Works with existing factory function
collector = create_metrics_collector(
    collector_type="schema",  # New type
    schema_class=MySchema
)

# Supports timing like other collectors
collector.start_timing()
metrics = collector.collect_metrics(data)
collector.end_timing()

# Compatible with existing save/load functions
from extraction.metrics import save_metrics_json
save_metrics_json(metrics, "analysis_results.json")
```

## Examples

See `examples/schema_metrics_example.py` for comprehensive examples including:
- Basic field type analysis
- Nested object handling
- Edge case management
- Data validation tracking
- Performance timing

## Testing

Comprehensive tests are available in `tests/metrics_test.py`:

```bash
python -m unittest tests.metrics_test.TestSchemaMetricsCollector -v
```

The test suite covers:
- All supported data types
- Optional field handling
- Nested object analysis
- Edge cases and error conditions
- Data validation scenarios
- Integration with factory functions
