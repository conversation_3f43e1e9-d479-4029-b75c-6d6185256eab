#!/usr/bin/env python3
"""
Example demonstrating the new SchemaMetricsCollector functionality.

This example shows how to use the schema-based metrics system to analyze
structured data against Pydantic schemas, providing comprehensive statistics
for different data types including numeric, boolean, string, list, and nested objects.
"""
from __future__ import annotations

import json
import sys
import os
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from extraction.metrics import SchemaMetricsCollector, LLMMetricsCollector, create_metrics_collector


# Define example schemas
class ProductFeature(BaseModel):
    """Schema for product features."""
    name: str
    enabled: bool
    priority: Optional[int] = None


class Product(BaseModel):
    """Schema for products."""
    name: str
    price: float
    in_stock: bool
    category: str
    features: List[str]
    description: Optional[str] = None
    rating: Optional[float] = None
    tags: List[str] = []


class Customer(BaseModel):
    """Schema for customers."""
    username: str
    age: int
    active: bool
    email: Optional[str] = None
    purchase_count: int = 0


class Order(BaseModel):
    """Schema for orders with nested objects."""
    order_id: str
    customer: Customer
    products: List[Product]
    total_amount: float
    status: str
    notes: Optional[str] = None


def generate_sample_products() -> List[Dict[str, Any]]:
    """Generate sample product data for analysis."""
    return [
        {
            "name": "Laptop Pro",
            "price": 1299.99,
            "in_stock": True,
            "category": "Electronics",
            "features": ["16GB RAM", "512GB SSD", "Intel i7"],
            "description": "High-performance laptop for professionals",
            "rating": 4.5,
            "tags": ["premium", "business", "portable"]
        },
        {
            "name": "Wireless Mouse",
            "price": 29.99,
            "in_stock": True,
            "category": "Accessories",
            "features": ["Bluetooth", "Ergonomic"],
            "description": None,
            "rating": 4.2,
            "tags": ["wireless", "ergonomic"]
        },
        {
            "name": "Gaming Keyboard",
            "price": 89.99,
            "in_stock": False,
            "category": "Gaming",
            "features": ["RGB Lighting", "Mechanical Keys", "Anti-ghosting"],
            "description": "Professional gaming keyboard with RGB lighting",
            "rating": None,
            "tags": []
        },
        {
            "name": "USB-C Hub",
            "price": 45.50,
            "in_stock": True,
            "category": "Accessories",
            "features": ["4 Ports", "USB 3.0"],
            "description": "Compact USB-C hub for modern laptops",
            "rating": 3.8,
            "tags": ["connectivity", "portable"]
        },
        {
            "name": "Monitor Stand",
            "price": 15.99,
            "in_stock": True,
            "category": "Accessories",
            "features": [],
            "description": "",  # Empty string
            "rating": 4.0,
            "tags": ["desk", "organization"]
        }
    ]


def generate_sample_customers() -> List[Dict[str, Any]]:
    """Generate sample customer data for analysis."""
    return [
        {
            "username": "john_doe",
            "age": 28,
            "active": True,
            "email": "<EMAIL>",
            "purchase_count": 5
        },
        {
            "username": "jane_smith",
            "age": 34,
            "active": True,
            "email": None,
            "purchase_count": 12
        },
        {
            "username": "bob_wilson",
            "age": 45,
            "active": False,
            "email": "<EMAIL>",
            "purchase_count": 0
        },
        {
            "username": "alice_brown",
            "age": 29,
            "active": True,
            "email": "<EMAIL>",
            "purchase_count": 8
        }
    ]


def analyze_products():
    """Demonstrate product schema analysis."""
    print("=" * 60)
    print("PRODUCT SCHEMA ANALYSIS")
    print("=" * 60)
    
    # Create schema metrics collector
    collector = SchemaMetricsCollector(Product, "product_analysis")
    
    # Generate sample data
    products = generate_sample_products()
    
    # Collect metrics
    metrics = collector.collect_metrics(products)
    
    # Display formatted summary
    print(collector.format_summary(metrics))
    
    # Display detailed metrics as JSON
    print("\n" + "=" * 40)
    print("DETAILED METRICS (JSON)")
    print("=" * 40)
    print(json.dumps(metrics, indent=2))
    
    return metrics


def analyze_customers():
    """Demonstrate customer schema analysis."""
    print("\n" + "=" * 60)
    print("CUSTOMER SCHEMA ANALYSIS")
    print("=" * 60)
    
    # Use factory function to create collector
    collector = create_metrics_collector(
        collector_type="schema",
        name="customer_analysis",
        schema_class=Customer
    )
    
    # Generate sample data
    customers = generate_sample_customers()
    
    # Collect and display metrics
    metrics = collector.collect_metrics(customers)
    
    print(collector.format_summary(metrics))
    
    return metrics


def analyze_nested_orders():
    """Demonstrate nested object analysis."""
    print("\n" + "=" * 60)
    print("NESTED ORDER SCHEMA ANALYSIS")
    print("=" * 60)
    
    collector = SchemaMetricsCollector(Order, "order_analysis")
    
    # Generate sample nested data
    orders = [
        {
            "order_id": "ORD-001",
            "customer": {
                "username": "john_doe",
                "age": 28,
                "active": True,
                "email": "<EMAIL>",
                "purchase_count": 5
            },
            "products": [
                {
                    "name": "Laptop Pro",
                    "price": 1299.99,
                    "in_stock": True,
                    "category": "Electronics",
                    "features": ["16GB RAM", "512GB SSD"],
                    "description": "High-performance laptop",
                    "rating": 4.5,
                    "tags": ["premium"]
                }
            ],
            "total_amount": 1299.99,
            "status": "completed",
            "notes": "Express delivery requested"
        },
        {
            "order_id": "ORD-002",
            "customer": {
                "username": "jane_smith",
                "age": 34,
                "active": True,
                "email": None,
                "purchase_count": 12
            },
            "products": [
                {
                    "name": "Wireless Mouse",
                    "price": 29.99,
                    "in_stock": True,
                    "category": "Accessories",
                    "features": ["Bluetooth"],
                    "description": None,
                    "rating": 4.2,
                    "tags": ["wireless"]
                },
                {
                    "name": "USB-C Hub",
                    "price": 45.50,
                    "in_stock": True,
                    "category": "Accessories",
                    "features": ["4 Ports"],
                    "description": "Compact hub",
                    "rating": 3.8,
                    "tags": ["connectivity"]
                }
            ],
            "total_amount": 75.49,
            "status": "pending",
            "notes": None
        }
    ]
    
    metrics = collector.collect_metrics(orders)
    
    print(collector.format_summary(metrics))
    
    return metrics


def demonstrate_combined_metrics():
    """Demonstrate using both LLMMetricsCollector and SchemaMetricsCollector together."""
    print("\n" + "=" * 60)
    print("COMBINED LLM + SCHEMA METRICS ANALYSIS")
    print("=" * 60)

    # Sample LLM extraction results (simulated)
    llm_results = [
        {
            "product": {"name": "Laptop", "price": 999.99, "in_stock": True, "features": ["16GB RAM"], "description": "Gaming laptop"},
            "input_tokens": 150,
            "output_tokens": 45,
            "processing_time": 0.8
        },
        {
            "product": {"name": "Mouse", "price": 29.99, "in_stock": False, "features": ["Wireless"], "description": None},
            "input_tokens": 120,
            "output_tokens": 38,
            "processing_time": 0.6
        },
        {
            "error": "Rate limit exceeded",
            "input_tokens": 140,
            "output_tokens": 0,
            "processing_time": 0.1
        }
    ]

    # 1. Use LLMMetricsCollector for LLM performance metrics (automatic timing)
    llm_collector = LLMMetricsCollector("combined_analysis", cost_per_input_token=0.001, cost_per_output_token=0.002)
    llm_metrics = llm_collector.collect_metrics(llm_results)

    print("=== LLM Performance Metrics ===")
    print(llm_collector.format_summary(llm_metrics))

    # 2. Use SchemaMetricsCollector for data quality analysis
    extracted_products = [result["product"] for result in llm_results if "product" in result]

    schema_collector = SchemaMetricsCollector(Product, "data_quality_analysis")
    schema_metrics = schema_collector.collect_metrics(extracted_products)

    print("\n=== Data Quality Metrics ===")
    print(schema_collector.format_summary(schema_metrics))

    # 3. Combined analysis summary
    print("\n" + "=" * 40)
    print("COMBINED ANALYSIS SUMMARY")
    print("=" * 40)
    print(f"LLM Performance:")
    print(f"  - Success Rate: {llm_metrics['success_rate']:.1%}")
    print(f"  - Total Cost: ${llm_metrics['total_cost']:.6f}")
    print(f"  - Avg Processing Time: {llm_metrics['avg_processing_time']:.2f}s")

    print(f"\nData Quality:")
    print(f"  - Objects Analyzed: {schema_metrics['total_objects']}")
    print(f"  - Schema: {schema_metrics['schema_name']}")
    print(f"  - Fields with None Values: {sum(1 for field in schema_metrics['field_metrics'].values() if field['none_count'] > 0)}")

    return {"llm_metrics": llm_metrics, "schema_metrics": schema_metrics}


def demonstrate_deep_nested_analysis():
    """Demonstrate deep nested collection analysis."""
    print("\n" + "=" * 60)
    print("DEEP NESTED COLLECTION ANALYSIS")
    print("=" * 60)

    class ComplexDataSchema(BaseModel):
        """Schema with deeply nested collections."""
        mixed_data: List[Any]
        nested_structure: Dict[str, List[Dict[str, Any]]]
        metrics_data: List[Dict[str, List[float]]]

    collector = SchemaMetricsCollector(ComplexDataSchema, "deep_nested_analysis")

    # Complex nested data with multiple levels
    complex_data = [
        {
            "mixed_data": [
                "text_value",
                42,
                True,
                [1, 2, 3, 4, 5],
                {"nested": "dictionary", "with": ["nested", "list"]},
                3.14159,
                False,
                ["another", "nested", "list"]
            ],
            "nested_structure": {
                "users": [
                    {"name": "Alice", "scores": [95, 87, 92], "active": True},
                    {"name": "Bob", "scores": [78, 85], "active": False}
                ],
                "products": [
                    {"title": "Laptop", "ratings": [4.5, 4.2, 4.8], "available": True},
                    {"title": "Mouse", "ratings": [4.0], "available": True}
                ]
            },
            "metrics_data": [
                {"response_times": [0.1, 0.2, 0.15], "cpu_usage": [45.2, 67.8, 23.1]},
                {"response_times": [0.3, 0.25], "cpu_usage": [89.5, 12.3, 56.7]}
            ]
        },
        {
            "mixed_data": [
                "another_string",
                100,
                [6, 7, 8],
                {"different": "structure", "numbers": [10, 20, 30]}
            ],
            "nested_structure": {
                "users": [
                    {"name": "Charlie", "scores": [88, 91, 85, 94], "active": True}
                ],
                "products": [
                    {"title": "Keyboard", "ratings": [4.3, 4.1, 4.6, 4.4], "available": False}
                ]
            },
            "metrics_data": [
                {"response_times": [0.18, 0.22], "cpu_usage": [34.5, 78.9]}
            ]
        }
    ]

    metrics = collector.collect_metrics(complex_data)

    print(collector.format_summary(metrics))

    # Show detailed analysis of nested structures
    print("\n" + "=" * 40)
    print("DETAILED NESTED ANALYSIS")
    print("=" * 40)

    # Mixed data analysis
    mixed_metrics = metrics["field_metrics"]["mixed_data"]["item_metrics"]
    print(f"\nMixed Data Type Distribution:")
    for type_name, count in mixed_metrics["type_distribution"].items():
        print(f"  {type_name}: {count} items")

    # Nested structure analysis
    nested_metrics = metrics["field_metrics"]["nested_structure"]["key_value_metrics"]
    print(f"\nNested Structure Analysis:")
    for key, key_metrics in nested_metrics.items():
        print(f"  {key}: {key_metrics['total_items']} items with {key_metrics['unique_types']} unique types")

    return metrics


def demonstrate_edge_cases():
    """Demonstrate handling of edge cases."""
    print("\n" + "=" * 60)
    print("EDGE CASES DEMONSTRATION")
    print("=" * 60)
    
    collector = SchemaMetricsCollector(Product, "edge_case_analysis")
    
    # Edge case data with various issues
    edge_case_data = [
        # Missing fields
        {
            "name": "Incomplete Product",
            "price": 10.0,
            "in_stock": True,
            "category": "Test"
            # Missing features, description, rating, tags
        },
        # Invalid data types
        {
            "name": "Invalid Product",
            "price": "not_a_number",  # Invalid price
            "in_stock": "maybe",      # Invalid boolean
            "category": "Test",
            "features": "not_a_list", # Invalid list
            "description": 123,       # Invalid string
            "rating": "five_stars",   # Invalid float
            "tags": ["valid", "tags"]
        },
        # All None values for optional fields
        {
            "name": "Minimal Product",
            "price": 5.0,
            "in_stock": False,
            "category": "Test",
            "features": [],
            "description": None,
            "rating": None,
            "tags": []
        }
    ]
    
    metrics = collector.collect_metrics(edge_case_data)
    
    print(collector.format_summary(metrics))
    
    # Show how invalid values are tracked
    print("\nInvalid Value Tracking:")
    for field_name, field_metrics in metrics["field_metrics"].items():
        if "invalid_values" in field_metrics and field_metrics["invalid_values"] > 0:
            print(f"  {field_name}: {field_metrics['invalid_values']} invalid values")
    
    return metrics


if __name__ == "__main__":
    print("Schema-Based Metrics Analysis Examples")
    print("=" * 60)
    
    # Run all examples
    product_metrics = analyze_products()
    customer_metrics = analyze_customers()
    order_metrics = analyze_nested_orders()
    combined_metrics = demonstrate_combined_metrics()
    deep_nested_metrics = demonstrate_deep_nested_analysis()
    edge_case_metrics = demonstrate_edge_cases()
    
    print("\n" + "=" * 60)
    print("ANALYSIS COMPLETE")
    print("=" * 60)
    print("All examples completed successfully!")
    print("Check the detailed JSON output above for comprehensive metrics.")
