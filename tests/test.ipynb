from __future__ import annotations

# Fix the import path for Jupyter notebook
import sys
import os

# Add the project root to Python path
# Assuming notebook is in extraction/examples/generic_extraction/
project_root = os.path.abspath('../../../')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from extraction.inference import get_language_model
from my_prompt import ProductPrompt, EvaluationPrompt
from my_language_model import CustomLanguageModel

api_key = os.getenv("OPENAI_API_KEY")

print('Imports successful!')

# Sample data
items = [
    {"text": "Widget X costs $19.99 and includes wifi, gps."},
    {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
]

# Build prompts
inputs = [ProductPrompt().build(x) for x in items]
print(f"Built {len(inputs)} prompts")
inputs

# Get language model and run extraction
lm = get_language_model(provider="custom")
results, _ = lm.generate(inputs)
print(f"Generated {len(results)} results")

results


def format_evaluation_record(record):
    input_prompt = record[0]["messages"][0]["content"]
    expected_format = record[0]["response_format"].model_json_schema()
    extraction_output = record[1]["product"]

    return {
        "input_prompt": input_prompt,
        "expected_format": expected_format,
        "extraction_output": extraction_output
    }

formatted_records = [EvaluationPrompt().build(format_evaluation_record(r))for r in zip(inputs, results)]



evaluation_results, _ = lm.generate(formatted_records)
evaluation_results

_

for i, r in enumerate(results):
    print(i, r.get("product"))

