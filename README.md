![paperscraper banner](assets/banner.svg)

# paperscraper

Collect arXiv papers with metadata, content, and AI-extracted author affiliations. Includes web visualization.

## Features

- Smart Collection: Fetch arXiv papers by category and date with automatic deduplication
- AI-Powered Analysis: Extract first-page content and author affiliations using LLMs
- Interactive Visualization: Web-based interface with search, filtering, and affiliation insights
- Flexible AI Backend: Support for OpenAI API, local Ollama, or custom providers
- Structured Storage: Organized data storage by category and date for easy access

## Installation

```bash
pip install -e .
```

## Quick Start

### Option 1: OpenAI API (Recommended)
Set your OpenAI API key for best results:
```bash
export OPENAI_API_KEY=your_key_here
```

### Option 2: Local Ollama (Free)
Install and run Ollama locally:
```bash
# Install Ollama from https://ollama.ai
ollama pull qwen3:4b-instruct  # or any other model
```

## Usage

### 1. Collect Papers

```bash
# Collect cs.AI papers for a specific date
paperscraper cs.AI 2024-01-15

# Limit to 5 papers (good for testing)
paperscraper cs.AI 2024-01-15 --num 5

# Use local Ollama instead of OpenAI
paperscraper cs.AI 2024-01-15 --provider ollama --model qwen3:4b-instruct
```

### 2. Visualize Results

```bash
# Launch interactive web interface (opens browser automatically)
paperscraper visualize
```

## How It Works (arXiv flow)

1. Fetch: Downloads metadata from arXiv API for specified category/date
2. Extract: Uses AI to parse first page and identify author affiliations
3. Store: Saves structured data in `data/category/YY/MM/DD/papers.json`
4. Visualize: Interactive view over the stored dataset

## Behavior Notes

- Papers are only stored in the category they were fetched for (even if arXiv lists multiple categories)
- When running `--steps affiliations`, the tool finds papers missing affiliations and processes just those
- Long operations show progress bars; use `--disable-progress` to turn them off

## Extending: Generic structured extractions

The core parts are generic and reusable:
- BaseLanguageModel and providers are in `paperscraper/inference.py` (batching is handled here)
- BasePromptBuilder is in `paperscraper/prompting.py`
- A tiny helper lives in `paperscraper/extraction.py` for building inputs

See the Developer Guide for creating your own extraction pipelines and the example templates in:
- `paperscraper/examples/generic_extraction/`

To use your own provider, implement BaseLanguageModel and register it:

```python
from paperscraper import BaseLanguageModel, register_language_model
class MyLM(BaseLanguageModel):
    ...
register_language_model("myprovider", lambda **kw: MyLM(**kw))
```

Then call: `get_language_model(provider="myprovider")` in your script.
